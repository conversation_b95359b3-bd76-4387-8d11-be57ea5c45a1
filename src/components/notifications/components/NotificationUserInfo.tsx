import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/Avatar';
import { NotificationUserInfoProps } from '../types';

export function NotificationUserInfo({
  displayName,
  avatarUrl,
  shortName,
}: NotificationUserInfoProps) {
  return (
    <div className='flex items-center gap-2 sm:gap-3 min-w-0'>
      <Avatar className='w-8 h-8 sm:w-10 sm:h-10 flex-shrink-0'>
        <AvatarImage src={avatarUrl} />
        <AvatarFallback className='bg-gradient-to-br from-purple-400 to-yellow-400 text-white text-xs sm:text-sm font-medium'>
          {shortName}
        </AvatarFallback>
      </Avatar>
      <span className='text-sm text-gray-900 font-medium truncate min-w-0'>{displayName}</span>
    </div>
  );
}
