import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/Avatar';
import { Badge } from '@/components/ui/Badge';
import { Button } from '@/components/ui/Button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/DropDownMenu';
import { Skeleton } from '@/components/ui/Skeleton';
import { AdminClubPost, ReportStatusEnum } from '@/generated/graphql';
import { Flag, Heart, MoreHorizontal } from 'lucide-react';
import { formatTimeAgo, getDisplayName } from '../../utils';
import { cn } from '@/lib/utils';

interface PostTableItemProps {
  post: AdminClubPost;
  isLoading?: boolean;
  showActions: boolean;
  onRemovePost: (postId: string) => void;
  onDisableClubAccess: (userId: string) => void;
  onUnflagPost: (postId: string) => void;
}

const PostTableItem = ({
  post,
  isLoading = false,
  showActions,
  onRemovePost,
  onDisableClubAccess,
  onUnflagPost,
}: PostTableItemProps) => {
  const hasOpenReports =
    post.reports && post.reports.some((report) => report.status === ReportStatusEnum.Open);
  const displayName = getDisplayName(
    post.clubProfile?.user?.firstName,
    post.clubProfile?.user?.lastName
  );

  const isDeleted = post.deletedAt !== null;

  const shortName = displayName
    .split(' ')
    .map((n) => n[0])
    .join('')
    .toUpperCase();

  if (isLoading) {
    return <PostTableItemSkeleton />;
  }

  return (
    <div
      key={post.id}
      className={cn(
        'p-4 sm:p-6 border-b',
        hasOpenReports ? 'bg-[#FCEFED80]' : 'border',
        isDeleted && 'bg-muted'
      )}
    >
      <div className='flex flex-col gap-4'>
        {/* Header with Avatar, Name, Reports, and Actions */}
        <div className='flex justify-between gap-4'>
          <div className='flex gap-2 min-w-0 flex-1'>
            {/* Avatar */}
            <Avatar
              key={`avatar-${post.id}-${post.clubProfile?.img?.url || 'fallback'}`}
              className='w-10 h-10 flex-shrink-0'
            >
              <AvatarImage src={post.clubProfile?.img?.url || ''} />
              <AvatarFallback className='bg-gradient-to-br from-purple-400 to-yellow-400 text-white text-sm font-medium'>
                {shortName}
              </AvatarFallback>
            </Avatar>

            {/* Header */}
            <div className='flex flex-col sm:flex-row items-start gap-2 sm:gap-4 w-full min-w-0'>
              <div className='flex flex-col sm:flex-row items-start gap-2 sm:gap-4 '>
                <div className='flex flex-col flex-shrink-0'>
                  <span className='font-medium text-sm text-gray-900'>{displayName}</span>
                  {post.clubProfile?.user?.role && (
                    <p className='text-xs text-muted-foreground'>
                      {post.clubProfile?.user?.role === 'ADMIN' ? 'Admin' : 'User'}
                    </p>
                  )}
                </div>
              </div>
              {/* Desktop - Keep reports inline */}
              {hasOpenReports && post.reports && post.reports.length > 0 && !isDeleted && (
                <div className='hidden sm:block flex-1 min-w-0'>
                  <div className='flex items-center gap-1 overflow-x-auto scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100 pb-1'>
                    <div className='flex items-center gap-1 flex-nowrap'>
                      {Array.from(
                        new Set(
                          post.reports
                            .filter((report) => report.status === ReportStatusEnum.Open)
                            .map((report) => report.category?.title || 'Other')
                        )
                      ).map((reportTitle, index) => {
                        return (
                          <Badge
                            key={`${reportTitle}-${index}`}
                            className='bg-[#FCEFED] gap-1 sm:gap-2 text-xs sm:text-sm text-destructive-foreground hover:bg-[#FCEFED] font-medium flex-shrink-0 whitespace-nowrap'
                          >
                            <Flag className='w-3 h-3 sm:w-4 sm:h-4 flex-shrink-0 text-destructive-foreground' />
                            {reportTitle}
                          </Badge>
                        );
                      })}
                    </div>
                  </div>
                </div>
              )}
              {isDeleted && (
                <div className='hidden sm:block flex-1 min-w-0'>
                  <div className='flex items-center gap-1 overflow-x-auto scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100 pb-1'>
                    <div className='flex items-center gap-1 flex-nowrap'>
                      <Badge className='bg-[#FCEFED] gap-1 sm:gap-2 text-xs sm:text-sm text-destructive-foreground hover:bg-[#FCEFED] font-medium flex-shrink-0 whitespace-nowrap'>
                        <Flag className='w-3 h-3 sm:w-4 sm:h-4 flex-shrink-0 text-destructive-foreground' />
                        Deleted
                      </Badge>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Actions */}
          {showActions && (
            <div className='flex-shrink-0'>
              <DropdownMenu>
                <DropdownMenuTrigger asChild disabled={isDeleted}>
                  <Button variant='ghost' size='sm' className='h-8 w-8 p-0'>
                    <MoreHorizontal className='w-6 h-6' />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align='end' className='min-w-60'>
                  {hasOpenReports && (
                    <>
                      <DropdownMenuItem
                        onClick={() => {
                          if (post.clubProfile?.user?.id) {
                            onDisableClubAccess(post.clubProfile.user.id);
                          }
                        }}
                        disabled={!post.clubProfile?.user?.canUseClubs}
                      >
                        Disable Clubs Access
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => onUnflagPost(post.id)}>
                        Unflag Post
                      </DropdownMenuItem>
                    </>
                  )}
                  <DropdownMenuItem onClick={() => onRemovePost(post.id)}>
                    Delete Post
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          )}
        </div>

        {/* Mobile - Reports below avatar and user name */}
        {hasOpenReports && post.reports && post.reports.length > 0 && !isDeleted && (
          <div className='block sm:hidden w-full'>
            <div className='flex items-center gap-1 overflow-x-auto scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100 pb-1'>
              <div className='flex items-center gap-1 flex-nowrap'>
                {Array.from(
                  new Set(
                    post.reports
                      .filter((report) => report.status === ReportStatusEnum.Open)
                      .map((report) => report.category?.title || 'Other')
                  )
                ).map((reportTitle, index) => {
                  return (
                    <Badge
                      key={`${reportTitle}-${index}`}
                      className='bg-[#FCEFED] gap-1 text-xs text-destructive-foreground hover:bg-[#FCEFED] font-medium flex-shrink-0 whitespace-nowrap'
                    >
                      <Flag className='w-3 h-3 flex-shrink-0 text-destructive-foreground' />
                      {reportTitle}
                    </Badge>
                  );
                })}
              </div>
            </div>
          </div>
        )}
        {isDeleted && (
          <div className='block sm:hidden w-full'>
            <div className='flex items-center gap-1 overflow-x-auto scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100 pb-1'>
              <div className='flex items-center gap-1 flex-nowrap'>
                <Badge className='bg-[#FCEFED] gap-1 text-xs text-destructive-foreground hover:bg-[#FCEFED] font-medium flex-shrink-0 whitespace-nowrap'>
                  <Flag className='w-3 h-3 flex-shrink-0 text-destructive-foreground' />
                  Deleted
                </Badge>
              </div>
            </div>
          </div>
        )}

        {/* Post Content - Full Width */}
        <div className='w-full'>
          <p className='text-sm'>{post.content}</p>
        </div>

        {/* Reports Display - Full Width */}
        {hasOpenReports &&
          post.reports &&
          post.reports.length > 0 &&
          (() => {
            // Filter reports with null category and get their details
            const customReports = post.reports
              .filter(
                (report) =>
                  report.category === null &&
                  report.details &&
                  report.status === ReportStatusEnum.Open
              )
              .map((report) => report.details!)
              .sort((a, b) => a.length - b.length); // Sort by length ascending

            if (customReports.length === 0) return null;

            return (
              <div className='w-full max-w-full'>
                <div
                  className={cn(
                    'w-full max-h-[120px] sm:max-h-full overflow-y-auto scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100 pr-2'
                  )}
                >
                  {customReports.length === 1 ? (
                    <p className='text-sm text-destructive-foreground break-words leading-relaxed'>
                      Report reason: - &quot;{customReports[0]}&quot;
                    </p>
                  ) : (
                    <div className='space-y-1 sm:space-y-2'>
                      <p className='text-sm text-destructive-foreground font-medium'>
                        Report reasons:
                      </p>
                      {customReports.map((reason, index) => (
                        <p
                          key={`reason-${index}`}
                          className='text-sm text-destructive-foreground break-words leading-relaxed pl-2'
                        >
                          - &quot;{reason}&quot;
                        </p>
                      ))}
                    </div>
                  )}
                </div>
              </div>
            );
          })()}

        {/* Footer with Time and Reactions */}
        <div className='flex items-center justify-between'>
          <span className='text-[13px] text-gray-500'>{formatTimeAgo(post.createdAt || '')}</span>
          <div className='flex items-center gap-4 text-sm text-gray-500'>
            <div className='flex items-center gap-1'>
              <Heart className='w-6 h-6 text-primary' />
              <span className='text-primary'>{post.reactionCount || 0}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PostTableItem;

const PostTableItemSkeleton = () => {
  return (
    <div className='p-4 sm:p-6'>
      <div className='flex flex-col sm:flex-row items-start gap-4'>
        {/* Avatar Skeleton */}
        <Skeleton className='w-10 h-10 rounded-full flex-shrink-0' />

        {/* Content Skeleton */}
        <div className='flex-1 min-w-0'>
          {/* Header Skeleton */}
          <div className='flex items-center gap-2 mb-2'>
            <Skeleton className='h-4 w-24' />
            <Skeleton className='h-5 w-12 rounded-full' />
          </div>

          {/* Post Content Skeleton */}
          <div className='mb-3 space-y-2'>
            <Skeleton className='h-4 w-full' />
            <Skeleton className='h-4 w-4/5' />
            <Skeleton className='h-4 w-3/5' />
          </div>

          {/* Footer Skeleton */}
          <div className='flex items-center justify-between'>
            <div className='flex items-center gap-4'>
              <Skeleton className='h-3 w-16' />
              <div className='flex items-center gap-1'>
                <Skeleton className='h-4 w-4 rounded' />
                <Skeleton className='h-3 w-4' />
              </div>
            </div>

            {/* Actions Skeleton */}
            <Skeleton className='h-8 w-8 rounded' />
          </div>
        </div>
      </div>
    </div>
  );
};
